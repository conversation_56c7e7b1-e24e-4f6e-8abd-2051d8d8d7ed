{"extends": ["stylelint-config-standard", "stylelint-config-rational-order"], "plugins": ["stylelint-order"], "rules": {"indentation": 2, "declaration-colon-space-after": "always", "declaration-colon-space-before": "never", "declaration-block-semicolon-newline-after": "always-multi-line", "selector-not-notation": null, "import-notation": null, "function-no-unknown": null, "selector-class-pattern": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "local"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "function", "if", "each", "include", "mixin", "extend"]}], "no-empty-source": null, "string-quotes": null, "named-grid-areas-no-invalid": null, "no-descending-specificity": null, "font-family-no-missing-generic-family-keyword": null, "rule-empty-line-before": ["always", {"ignore": ["after-comment", "first-nested"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "order/order": [["dollar-variables", "custom-properties", "at-rules", "declarations", {"type": "at-rule", "name": "supports"}, {"type": "at-rule", "name": "media"}, "rules"], {"severity": "error"}]}}