{"name": "cupl-spa", "private": true, "version": "0.0.0", "type": "module", "homepage": "", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "husky install", "lint:css": "stylelint '**/*.css'"}, "dependencies": {"@ant-design/cssinjs": "^1.17.2", "@ant-design/happy-work-theme": "^1.0.0", "@ant-design/icons": "^5.3.7", "@fortawesome/fontawesome-free": "^6.5.2", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/timeline": "^6.1.9", "@iconify/icons-ant-design": "^1.2.7", "@iconify/react": "^4.1.1", "@react-pdf/renderer": "^3.4.4", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@vercel/analytics": "^1.2.2", "@vitejs/plugin-react": "^4.1.0", "antd": "^5.19.4", "apexcharts": "^3.43.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.2", "bootstrap": "^5.3.3", "boxicons": "^2.1.4", "browser-image-compression": "^2.0.2", "classnames": "^2.3.2", "color": "^4.2.3", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "highlight.js": "^11.9.0", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^4.0.0", "localforage": "^1.10.0", "node-forge": "^1.3.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "papaparse": "^5.4.1", "ramda": "^0.29.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.2", "react-datepicker": "^7.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-error-boundary": "^4.0.13", "react-helmet-async": "^1.3.0", "react-i18next": "^13.2.2", "react-icons": "^4.11.0", "react-loading-skeleton": "^3.4.0", "react-markdown": "^8.0.7", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-router-dom": "^6.23.1", "react-select": "^5.8.0", "react-use": "^17.4.0", "rebass": "^4.0.7", "recharts": "^2.12.7", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "reset-css": "^5.0.2", "screenfull": "^6.0.2", "simplebar-react": "^3.2.6", "style": "^0.0.3", "styled-components": "^6.0.9", "vite": "^4.4.11", "xlsx": "^0.18.5", "xlsx-js": "^0.8.7", "zustand": "^4.4.3"}, "devDependencies": {"@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@faker-js/faker": "^8.1.0", "@types/autosuggest-highlight": "^3.2.0", "@types/color": "^3.0.4", "@types/nprogress": "^0.2.1", "@types/numeral": "^2.0.3", "@types/ramda": "^0.29.6", "@types/react": "^18.2.28", "@types/react-beautiful-dnd": "^13.1.6", "@types/react-dom": "^18.2.13", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.28", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.16", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "msw": "^2.3.1", "postcss": "^8.4.31", "postcss-import": "^15.1.0", "postcss-nesting": "^11.3.0", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.69.3", "stylelint": "^15.11.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^33.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.7.0", "stylelint-order": "^6.0.3", "tailwindcss": "^3.3.3", "terser": "^5.26.0", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite-plugin-svg-icons": "^2.0.1", "vite-tsconfig-paths": "^4.2.1"}, "msw": {"workerDirectory": "public"}}