@import url(https://fonts.googleapis.com/css?family=Roboto+Condensed:400,700);
/* written by r<PERSON><PERSON> balogun https://www.facebook.com/riliwan.rabo*/
.board-pq{
    width: 75%;
    margin: 60px auto;
    height: 500px;
    background: #fff;
    /*box-shadow: 10px 10px #ccc,-10px 20px #ddd;*/
}
.board-pq .nav-tabs-pq {
    position: relative;
    /* border-bottom: 0; */
    /* width: 80%; */
    margin: 40px auto;
    margin-bottom: 0;
    box-sizing: border-box;
}

.board-pq > div.board-inner-pq{
    background: #fafafa url(https://subtlepatterns.com/patterns/geometry2.png);
    background-size: 30%;
}

p.narrow-pq{
    width: 60%;
    margin: 10px auto;
}

.liner-pq{
    height: 2px;
    background: #ddd;
    position: absolute;
    width: 80%;
    margin: 0 auto;
    left: 0;
    right: 0;
    top: 50%;
    z-index: 1;
}

.nav-tabs-pq > li.active > a, .nav-tabs-pq > li.active > a:hover, .nav-tabs-pq > li.active > a:focus {
    color: #555555;
    cursor: default;
    /* background-color: #ffffff; */
    border: 0;
    border-bottom-color: transparent;
}

span.round-tabs-pq{
    width: 70px;
    height: 70px;
    line-height: 70px;
    display: inline-block;
    border-radius: 100px;
    background: white;
    z-index: 2;
    position: absolute;
    left: 0;
    text-align: center;
    font-size: 25px;
}

span.round-tabs.one-pq{
    color: rgb(34, 194, 34);border: 2px solid rgb(34, 194, 34);
}

li.active span.round-tabs.one-pq{
    background: #fff !important;
    border: 2px solid #ddd;
    color: rgb(34, 194, 34);
}

span.round-tabs.two-pq{
    color: #febe29;border: 2px solid #febe29;
}

li.active span.round-tabs.two-pq{
    background: #fff !important;
    border: 2px solid #ddd;
    color: #febe29;
}

span.round-tabs.three-pq{
    color: #3e5e9a;border: 2px solid #3e5e9a;
}

li.active span.round-tabs.three-pq{
    background: #fff !important;
    border: 2px solid #ddd;
    color: #3e5e9a;
}

span.round-tabs.four-pq{
    color: #f1685e;border: 2px solid #f1685e;
}

li.active span.round-tabs.four-pq{
    background: #fff !important;
    border: 2px solid #ddd;
    color: #f1685e;
}

span.round-tabs.five-pq{
    color: #999;border: 2px solid #999;
}

li.active span.round-tabs.five-pq{
    background: #fff !important;
    border: 2px solid #ddd;
    color: #999;
}

.nav-tabs-pq > li.active > a span.round-tabs-pq{
    background: #fafafa;
}
.nav-tabs-pq > li {
    width: 20%;
}
/*li.active:before {
    content: " ";
    position: absolute;
    left: 45%;
    opacity:0;
    margin: 0 auto;
    bottom: -2px;
    border: 10px solid transparent;
    border-bottom-color: #fff;
    z-index: 1;
    transition:0.2s ease-in-out;
}*/
.nav-tabs-pq > li:after {
    content: " ";
    position: absolute;
    left: 45%;
    opacity:0;
    margin: 0 auto;
    bottom: 0px;
    border: 5px solid transparent;
    border-bottom-color: #ddd;
    transition:0.1s ease-in-out;
}
.nav-tabs-pq > li.active:after {
    content: " ";
    position: absolute;
    left: 45%;
    opacity:1;
    margin: 0 auto;
    bottom: 0px;
    border: 10px solid transparent;
    border-bottom-color: #ddd;
}
.nav-tabs-pq > li a{
    width: 70px;
    height: 70px;
    margin: 20px auto;
    border-radius: 100%;
    padding: 0;
}

.nav-tabs-pq > li a:hover{
    background: transparent;
}

.tab-pane-pq{
    position: relative;
    padding-top: 50px;
}
.tab-content-pq .head-pq{
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 25px;
    text-transform: uppercase;
    padding-bottom: 10px;
}
.btn-outline-rounded-pq{
    padding: 10px 40px;
    margin: 20px 0;
    border: 2px solid transparent;
    border-radius: 25px;
}

.btn.green-pq{
    background-color:#5cb85c;
    /*border: 2px solid #5cb85c;*/
    color: #ffffff;
}

@media( max-width : 585px ){
    .board-pq {
        width: 90%;
        height:auto !important;
    }
    span.round-tabs-pq {
        font-size:16px;
        width: 50px;
        height: 50px;
        line-height: 50px;
    }
    .tab-content-pq .head-pq{
        font-size:20px;
    }
    .nav-tabs-pq > li a {
        width: 50px;
        height: 50px;
        line-height:50px;
    }
    .nav-tabs-pq > li.active:after {
        content: " ";
        position: absolute;
        left: 35%;
    }
    .btn-outline-rounded-pq {
        padding:12px 20px;
    }
}
.modal-header-pq,
.modal-body-pq,
.modal-footer-pq {
    padding: 20px;
}

.alert-success {
    color: green;
    font-weight: bold;
  }
