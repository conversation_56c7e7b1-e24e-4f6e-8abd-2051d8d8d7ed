a:hover,
a:focus {
  text-decoration: none;
  outline: none;
}

.tab {
  font-family: 'N<PERSON><PERSON>', sans-serif;
}

.tab .nav-tabs {
  background-color: transparent;
  border: none;

  margin-top: 1rem;
  left: 0;
  width: 100%;
  z-index: 1000;
  margin-left: 8rem;
}

.tab .nav-tabs .nav-link {
  color: #222;
  background: transparent;
  font-size: 18px;
  font-weight: 800;
  letter-spacing: 1px;
  text-align: center;
  text-transform: uppercase;
  padding: 15px 15px 10px;
  margin: 0;
  border: none;
  border-radius: 0;
  overflow: hidden;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease 0s;
}

/* .tab .nav-tabs .nav-link:hover,
.tab .nav-tabs .nav-link.active {
  color: #222;
  background: #fff;
  border: none;
} */

/* .tab .nav-tabs .nav-link.active {
  color: #6CBF1C;
} */

/* .tab .nav-tabs .nav-link::before,
.tab .nav-tabs .nav-link::after {
  content: "";
  background-color: #d1d1d1;
  height: 7px;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  transition: all 0.5s ease 0s;
}

.tab .nav-tabs .nav-link::after {
  background-color: #6CBF1C;
  height: 100%;
  opacity: 0;
} */

.tab .nav-tabs .nav-link.active::before,
.tab .nav-tabs .nav-link:hover::before {
  height: 100%;
  opacity: 0;
}

.tab .nav-tabs .nav-link.active::after,
.tab .nav-tabs .nav-link:hover::after {
  height: 7px;
  opacity: 1;
}

.tab .tab-content {
  color: #555;
  background: #fff;
  font-size: 15px;
  letter-spacing: 1px;
  line-height: 23px;
  padding: 20px;
  margin-top: 2rem;
  /* Adjust margin-top as needed */
}


.tab .tab-content h2 {
  margin-top: 2rem;
  /* Adjust margin-top as needed */
}

.tab .tab-content h3 {
  color: #222;
  font-size: 22px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 7px 0;
}

@media only screen and (max-width: 479px) {
  .tab .nav-tabs .nav-item {
    width: 100%;
  }

  .tab .nav-tabs .nav-link {
    margin: 0 0 10px;
  }

  .tab .tab-content h3 {
    font-size: 18px;
  }
}