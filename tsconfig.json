{
  "compilerOptions": {
    "noEmitOnError": false,
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "useDefineForClassFields": true,
    "skipLibCheck": true,
    "allowJs": true,  // Allow compilation of JavaScript files
    "moduleResolution": "bundler",
    "sourceMap": true,
    "declaration": true,
    "preserveWatchOutput": true,
    "removeComments": true,
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "strict": true,
    "strictNullChecks": true,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "useUnknownInCatchVariables": false,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["types/*"]
    }
  },
  "include": ["src", "test", "types/**/*.ts", "**.ts", "*.json", "**/*.js", "**/*.jsx"],
  "exclude": ["node_modules", "dist"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
